
import { useParams } from "react-router-dom";
import NavBar from "@/components/layouts/NavBar";
import TutorialViewer from "@/components/TutorialViewer";
import RegenerateCoverButton from "@/components/RegenerateCoverButton";
import GitHubRepoCard from "@/components/GitHubRepoCard";
import { useTutorialDetails } from "@/hooks/useTutorialDetails";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useState } from "react";

const TutorialDetailsSkeleton = () => (
  <div className="container mx-auto py-6 px-4 max-w-full">
    <div className="mb-6">
      <Skeleton className="h-48 w-full mb-4 rounded-lg" />
      <Skeleton className="h-10 w-3/4 mb-2" />
      <Skeleton className="h-5 w-full mb-2" />
      <Skeleton className="h-5 w-1/2" />
    </div>
    <div className="flex flex-col md:flex-row gap-6">
      <Skeleton className="h-[calc(100vh-200px)] md:w-64" />
      <Skeleton className="h-[calc(100vh-200px)] flex-1 w-full" />
    </div>
  </div>
);

const TutorialDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { tutorial, loading, error } = useTutorialDetails(id || "");
  const [refreshKey, setRefreshKey] = useState(0);

  const handleCoverRegenerated = () => {
    // Force a refresh of the tutorial data
    setRefreshKey(prev => prev + 1);
    // Reload the page to see the new cover image
    window.location.reload();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <NavBar />
        <TutorialDetailsSkeleton />
      </div>
    );
  }

  if (error || !tutorial) {
    return (
      <div className="min-h-screen flex flex-col">
        <NavBar />
        <main className="flex-1 container mx-auto py-12 px-4">
          <Alert variant="destructive" className="max-w-xl mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || "Tutorial not found"}
            </AlertDescription>
          </Alert>
        </main>
      </div>
    );
  }

  console.log("Tutorial:", tutorial);
  const tutorialContent = {
    id: tutorial.id,
    title: tutorial.title,
    description: tutorial.description,
    repoUrl: tutorial.repoUrl,
    chapters: [
      // Add Project Overview as the first item
      {
        id: "project_overview",
        title: `${tutorial.title} Overview`,
        content: tutorial.indexContent || "# Loading content..."
      },
      // Include all other chapters
      ...tutorial.chapters.map(chapter => ({
        id: chapter.filename,
        title: chapter.filename
          .replace(/^\d+_|_/g, ' ')
          .replace(/\.md$/, '')
          .trim()
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' '),
        content: chapter.content || "# Loading content..."
      }))
    ]
  };

  return (
    <div className="min-h-screen flex flex-col">
      <NavBar />
      <div className="container mx-auto py-6 px-4 max-w-full">
        {/* Cover Image Section */}
        {tutorial.imageSrc && (
          <div className="mb-6">
            <div className="relative w-full h-48 md:h-64 rounded-lg overflow-hidden shadow-lg">
              <img
                src={tutorial.imageSrc}
                alt={`${tutorial.title} cover`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Hide the image container if the image fails to load
                  const target = e.target as HTMLImageElement;
                  const container = target.closest('.relative');
                  if (container) {
                    (container.parentElement as HTMLElement).style.display = 'none';
                  }
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
              <div className="absolute bottom-4 left-4 right-4">
                <h1 className="text-white text-2xl md:text-3xl font-bold mb-2">{tutorial.title}</h1>
                {tutorial.description && (
                  <p className="text-white/90 text-sm md:text-base">{tutorial.description}</p>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="mb-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              {/* Only show title and description if no cover image */}
              {!tutorial.imageSrc && (
                <>
                  <h1 className="text-3xl font-bold mb-2">{tutorial.title}</h1>
                  <p className="text-muted-foreground mb-2">{tutorial.description}</p>
                </>
              )}
              {tutorial.repoUrl && (
                <GitHubRepoCard
                  repoUrl={tutorial.repoUrl}
                  className="mb-4"
                />
              )}
            </div>
            <RegenerateCoverButton
              tutorialId={tutorial.id.toString()}
              userId={tutorial.userId}
              onSuccess={handleCoverRegenerated}
            />
          </div>
        </div>
        <TutorialViewer tutorial={tutorialContent} />
      </div>
    </div>
  );
};

export default TutorialDetails;
