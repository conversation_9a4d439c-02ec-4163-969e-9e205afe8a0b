
import { useParams } from "react-router-dom";
import NavBar from "@/components/layouts/NavBar";
import TutorialViewer from "@/components/TutorialViewer";
import RegenerateCoverButton from "@/components/RegenerateCoverButton";
import GitHubRepoCard from "@/components/GitHubRepoCard";
import { useTutorialDetails } from "@/hooks/useTutorialDetails";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useState } from "react";

const TutorialDetailsSkeleton = () => (
  <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header Section Skeleton */}
      <div className="mb-8">
        <Skeleton className="h-64 w-full mb-6 rounded-xl" />
        <div className="max-w-4xl">
          <Skeleton className="h-12 w-3/4 mb-4" />
          <Skeleton className="h-6 w-full mb-2" />
          <Skeleton className="h-6 w-2/3 mb-6" />
          <Skeleton className="h-32 w-full rounded-lg" />
        </div>
      </div>

      {/* Content Section Skeleton */}
      <div className="flex flex-col lg:flex-row gap-8">
        <div className="lg:w-80">
          <Skeleton className="h-[calc(100vh-300px)] w-full rounded-xl" />
        </div>
        <div className="flex-1">
          <Skeleton className="h-[calc(100vh-300px)] w-full rounded-xl" />
        </div>
      </div>
    </div>
  </div>
);

const TutorialDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { tutorial, loading, error } = useTutorialDetails(id || "");
  const [refreshKey, setRefreshKey] = useState(0);

  const handleCoverRegenerated = () => {
    // Force a refresh of the tutorial data
    setRefreshKey(prev => prev + 1);
    // Reload the page to see the new cover image
    window.location.reload();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <NavBar />
        <TutorialDetailsSkeleton />
      </div>
    );
  }

  if (error || !tutorial) {
    return (
      <div className="min-h-screen flex flex-col">
        <NavBar />
        <main className="flex-1 bg-gradient-to-br from-gray-50 to-white">
          <div className="container mx-auto py-20 px-4 max-w-7xl">
            <div className="max-w-xl mx-auto text-center">
              <Alert variant="destructive" className="shadow-lg border-0">
                <AlertCircle className="h-5 w-5" />
                <AlertTitle className="text-lg font-semibold">Tutorial Not Found</AlertTitle>
                <AlertDescription className="text-base mt-2">
                  {error || "The tutorial you're looking for doesn't exist or has been removed."}
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </main>
      </div>
    );
  }

  console.log("Tutorial:", tutorial);
  const tutorialContent = {
    id: tutorial.id,
    title: tutorial.title,
    description: tutorial.description,
    repoUrl: tutorial.repoUrl,
    chapters: [
      // Add Project Overview as the first item
      {
        id: "project_overview",
        title: `${tutorial.title} Overview`,
        content: tutorial.indexContent || "# Loading content..."
      },
      // Include all other chapters
      ...tutorial.chapters.map(chapter => ({
        id: chapter.filename,
        title: chapter.filename
          .replace(/^\d+_|_/g, ' ')
          .replace(/\.md$/, '')
          .trim()
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' '),
        content: chapter.content || "# Loading content..."
      }))
    ]
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-gray-50 to-white">
      <NavBar />

      {/* Main Content Container */}
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8 max-w-7xl">

          {/* Hero Section */}
          <div className="mb-12">
            {tutorial.imageSrc ? (
              /* Cover Image Hero */
              <div className="relative w-full h-80 lg:h-96 rounded-2xl overflow-hidden shadow-2xl mb-8">
                <img
                  src={tutorial.imageSrc}
                  alt={`${tutorial.title} cover`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    const container = target.closest('.relative');
                    if (container) {
                      (container.parentElement as HTMLElement).style.display = 'none';
                    }
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
                <div className="absolute bottom-8 left-8 right-8">
                  <div className="max-w-4xl">
                    <h1 className="text-white text-3xl lg:text-5xl font-bold mb-4 leading-tight">
                      {tutorial.title}
                    </h1>
                    {tutorial.description && (
                      <p className="text-white/90 text-lg lg:text-xl leading-relaxed max-w-3xl">
                        {tutorial.description}
                      </p>
                    )}
                  </div>
                </div>

                {/* Regenerate Cover Button - Positioned in top right */}
                <div className="absolute top-6 right-6">
                  <RegenerateCoverButton
                    tutorialId={tutorial.id.toString()}
                    userId={tutorial.userId}
                    onSuccess={handleCoverRegenerated}
                  />
                </div>
              </div>
            ) : (
              /* Text-based Hero */
              <div className="text-center py-16 px-8">
                <div className="max-w-4xl mx-auto">
                  <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                    {tutorial.title}
                  </h1>
                  {tutorial.description && (
                    <p className="text-xl lg:text-2xl text-gray-600 leading-relaxed mb-8">
                      {tutorial.description}
                    </p>
                  )}

                  {/* Regenerate Cover Button */}
                  <div className="flex justify-center mb-8">
                    <RegenerateCoverButton
                      tutorialId={tutorial.id.toString()}
                      userId={tutorial.userId}
                      onSuccess={handleCoverRegenerated}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* GitHub Repository Card */}
            {tutorial.repoUrl && (
              <div className="max-w-4xl mx-auto">
                <GitHubRepoCard
                  repoUrl={tutorial.repoUrl}
                  className="shadow-lg border-0"
                />
              </div>
            )}
          </div>

          {/* Tutorial Content */}
          <div className="max-w-full">
            <TutorialViewer tutorial={tutorialContent} />
          </div>
        </div>
      </main>
    </div>
  );
};

export default TutorialDetails;
