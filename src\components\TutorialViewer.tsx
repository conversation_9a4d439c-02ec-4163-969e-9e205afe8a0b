
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch";
import { FileText, Eye } from "lucide-react";
import MarkdownRenderer from "./MarkdownRenderer";

export type Chapter = {
  id: string;
  title: string;
  content: string;
};

export type TutorialContent = {
  id: string;
  title: string;
  description: string;
  repoUrl: string;
  chapters: Chapter[];
};

type TutorialViewerProps = {
  tutorial: TutorialContent;
};

const TutorialViewer = ({ tutorial }: TutorialViewerProps) => {
  // Default to project overview (first chapter) when available
  const [activeChapter, setActiveChapter] = useState<string>(tutorial.chapters[0]?.id || "");
  const [showMarkdownSource, setShowMarkdownSource] = useState(false);

  // Extract mermaid diagrams from chapter content
  const extractMermaidDiagrams = (content: string): string[] => {
    const mermaidRegex = /```mermaid([\s\S]*?)```/g;
    const matches = [...content.matchAll(mermaidRegex)];
    return matches.map(match => match[1].trim());
  };

  // Extract code blocks (excluding mermaid) from chapter content
  const extractCodeBlocks = (content: string): { language: string, code: string }[] => {
    const codeBlockRegex = /```([\w-]*)\n([\s\S]*?)```/g;
    const matches = [...content.matchAll(codeBlockRegex)];
    return matches
      .filter(match => match[1] !== 'mermaid')
      .map(match => ({
        language: match[1].trim() || 'plaintext',
        code: match[2].trim()
      }));
  };

  const activeChapterContent = tutorial.chapters.find(chapter => chapter.id === activeChapter)?.content || "";
  const mermaidDiagrams = extractMermaidDiagrams(activeChapterContent);
  const codeBlocks = extractCodeBlocks(activeChapterContent);

  return (
    <div className="flex flex-col xl:flex-row w-full gap-6 animate-fade-in">
      {/* Table of Contents Sidebar */}
      <div className="xl:w-[320px] flex-shrink-0">
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm sticky top-6 h-[calc(100vh-180px)]">
          <CardContent className="p-6 h-full flex flex-col">
            <div className="flex items-center mb-6">
              <div className="w-2 h-8 bg-gradient-to-b from-tutorial-primary to-tutorial-secondary rounded-full mr-3"></div>
              <h3 className="text-lg font-semibold text-gray-900">Table of Contents</h3>
            </div>
            <ScrollArea className="flex-1 overflow-x-hidden">
              <nav className="space-y-2 pr-2">
                {tutorial.chapters.map((chapter, index) => {
                  // Calculate the correct chapter number (skip counting project_overview)
                  const chapterNumber = chapter.id === "project_overview" ? null :
                    tutorial.chapters.findIndex(ch => ch.id === "project_overview") === 0 ?
                      index : index + 1;

                  return (
                    <div key={chapter.id} className="overflow-hidden">
                      <button
                        onClick={() => setActiveChapter(chapter.id)}
                        className={`w-full text-left px-4 py-3 text-sm rounded-xl transition-all duration-200 truncate group ${
                          activeChapter === chapter.id
                            ? "bg-gradient-to-r from-tutorial-primary to-tutorial-secondary text-white shadow-lg transform scale-[1.02]"
                            : "hover:bg-gray-100 hover:shadow-md hover:transform hover:scale-[1.01] text-gray-700"
                        }`}
                        title={chapter.title}
                      >
                        <div className="flex items-center">
                          {chapterNumber && (
                            <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium mr-3 ${
                              activeChapter === chapter.id
                                ? "bg-white/20 text-white"
                                : "bg-gray-200 text-gray-600 group-hover:bg-tutorial-primary group-hover:text-white"
                            }`}>
                              {chapterNumber}
                            </span>
                          )}
                          <span className="font-medium">
                            {chapter.id === "project_overview" ? chapter.title : chapter.title}
                          </span>
                        </div>
                      </button>
                    </div>
                  );
                })}
              </nav>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 min-w-0">
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm h-[calc(100vh-180px)]">
          <CardContent className="p-6 h-full flex flex-col overflow-hidden">
            <Tabs defaultValue="content" className="w-full h-full flex flex-col">
              {/* Header with Tabs and Toggle */}
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4 flex-shrink-0">
                <TabsList className="bg-gray-100/80 p-1 rounded-xl shadow-inner">
                  <TabsTrigger
                    value="content"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg px-4 py-2 font-medium transition-all text-sm"
                  >
                    Content
                  </TabsTrigger>
                  <TabsTrigger
                    value="diagrams"
                    disabled={mermaidDiagrams.length === 0}
                    className="data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg px-4 py-2 font-medium transition-all disabled:opacity-50 text-sm"
                  >
                    Diagrams
                  </TabsTrigger>
                  <TabsTrigger
                    value="code"
                    disabled={codeBlocks.length === 0}
                    className="data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg px-4 py-2 font-medium transition-all disabled:opacity-50 text-sm"
                  >
                    Code Examples
                  </TabsTrigger>
                </TabsList>

                {/* Markdown Toggle Switch */}
                <div className="flex items-center space-x-3 bg-white/90 backdrop-blur-sm p-2.5 rounded-xl shadow-lg border border-gray-200/50">
                  <Eye className={`h-4 w-4 transition-colors ${!showMarkdownSource ? 'text-tutorial-primary' : 'text-gray-400'}`} />
                  <Switch
                    id="markdown-toggle"
                    checked={showMarkdownSource}
                    onCheckedChange={setShowMarkdownSource}
                    className="data-[state=checked]:bg-tutorial-primary"
                  />
                  <FileText className={`h-4 w-4 transition-colors ${showMarkdownSource ? 'text-tutorial-primary' : 'text-gray-400'}`} />
                  <span className="text-sm font-medium text-gray-700 ml-1">
                    {showMarkdownSource ? 'Markdown' : 'Preview'}
                  </span>
                </div>
              </div>
              {/* Content Tab */}
              <TabsContent value="content" className="mt-0 flex-1 flex flex-col overflow-hidden h-full">
                <div className="bg-gradient-to-br from-gray-50/50 to-white/50 rounded-2xl border border-gray-200/50 flex-1 flex flex-col overflow-hidden h-full">
                  <ScrollArea className="flex-1 overflow-x-hidden w-full h-full">
                    <div className="p-6 w-full max-w-full overflow-hidden">
                      {tutorial.chapters.map((chapter) => (
                        <div
                          key={chapter.id}
                          className={activeChapter === chapter.id ? "block w-full overflow-hidden" : "hidden"}
                          style={{ maxWidth: '100%' }}
                        >
                          <div className="prose prose-lg max-w-none prose-enhanced">
                            <MarkdownRenderer content={chapter.content} showMarkdownSource={showMarkdownSource} />
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              </TabsContent>
              {/* Diagrams Tab */}
              <TabsContent value="diagrams" className="mt-0 flex-1 flex flex-col overflow-hidden h-full">
                <div className="bg-gradient-to-br from-blue-50/50 to-white/50 rounded-2xl border border-blue-200/50 flex-1 flex flex-col overflow-hidden h-full">
                  <div className="flex items-center p-6 pb-4 flex-shrink-0">
                    <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full mr-3"></div>
                    <h2 className="text-xl font-bold text-gray-900">
                      Diagrams from {tutorial.chapters.find(ch => ch.id === activeChapter)?.title}
                    </h2>
                  </div>
                  <ScrollArea className="flex-1 overflow-x-hidden h-full">
                    <div className="px-6 pb-6">
                      <div className={activeChapter ? "block w-full overflow-hidden" : "hidden"}>
                        {mermaidDiagrams.length > 0 ? (
                          <div className="space-y-6">
                            {mermaidDiagrams.map((diagram, i) => (
                              <div key={`diagram-${i}`} className="bg-white rounded-xl p-6 shadow-lg border border-gray-200/50 overflow-hidden">
                                <div className="mb-3">
                                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Diagram {i + 1}
                                  </span>
                                </div>
                                <MarkdownRenderer content={`\`\`\`mermaid\n${diagram}\n\`\`\``} showMarkdownSource={showMarkdownSource} />
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-12 h-full flex flex-col justify-center">
                            <div className="text-gray-400 mb-2">
                              <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 3a1 1 0 000 2h.01a1 1 0 100-2H5zm4 0a1 1 0 000 2h.01a1 1 0 100-2H9zm2 0a1 1 0 000 2h.01a1 1 0 100-2H11zm0 2a1 1 0 000 2h.01a1 1 0 100-2H11zm-2 0a1 1 0 000 2h.01a1 1 0 100-2H9zm-2 0a1 1 0 000 2h.01a1 1 0 100-2H7zm2 2a1 1 0 000 2h.01a1 1 0 100-2H9z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <p className="text-gray-500 font-medium">No diagrams found in this chapter</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </ScrollArea>
                </div>
              </TabsContent>
              {/* Code Examples Tab */}
              <TabsContent value="code" className="mt-0 flex-1 flex flex-col overflow-hidden h-full">
                <div className="bg-gradient-to-br from-green-50/50 to-white/50 rounded-2xl border border-green-200/50 flex-1 flex flex-col overflow-hidden h-full">
                  <div className="flex items-center p-6 pb-4 flex-shrink-0">
                    <div className="w-2 h-8 bg-gradient-to-b from-green-500 to-green-600 rounded-full mr-3"></div>
                    <h2 className="text-xl font-bold text-gray-900">
                      Code Examples from {tutorial.chapters.find(ch => ch.id === activeChapter)?.title}
                    </h2>
                  </div>
                  <ScrollArea className="flex-1 overflow-x-hidden h-full">
                    <div className="px-6 pb-6">
                      <div className={activeChapter ? "block w-full overflow-hidden" : "hidden"}>
                        {codeBlocks.length > 0 ? (
                          <div className="space-y-6">
                            {codeBlocks.map((block, i) => (
                              <div key={`code-block-${i}`} className="bg-white rounded-xl shadow-lg border border-gray-200/50 overflow-hidden">
                                <div className="bg-gradient-to-r from-gray-800 to-gray-900 text-gray-200 px-6 py-3 flex items-center justify-between">
                                  <div className="flex items-center space-x-2">
                                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                      {block.language || 'plaintext'}
                                    </span>
                                    <span className="text-sm font-medium">Example {i + 1}</span>
                                  </div>
                                  <div className="flex space-x-1">
                                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                  </div>
                                </div>
                                <div className="overflow-x-auto">
                                  <pre className="bg-gray-900 text-gray-100 p-6 text-sm leading-relaxed">
                                    <code>{block.code}</code>
                                  </pre>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-12 h-full flex flex-col justify-center">
                            <div className="text-gray-400 mb-2">
                              <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <p className="text-gray-500 font-medium">No code examples found in this chapter</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </ScrollArea>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TutorialViewer;
