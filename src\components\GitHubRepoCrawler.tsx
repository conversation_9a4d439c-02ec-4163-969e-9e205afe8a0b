// doc: https://docs.github.com/en/rest/git/trees?apiVersion=2022-11-28

import React, { useState, useEffect, useCallback, useRef } from "react";
import { Octokit } from "@octokit/rest";
import { Checkbox } from "@/components/ui/checkbox";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  ChevronRight,
  ChevronDown,
  Folder,
  FileText,
  ChevronUp,
} from "lucide-react";
import { minimatch } from "minimatch";
import { toast } from "@/hooks/use-toast";

// Add RepoConfig interface
interface RepoConfig {
  owner: string;
  repository: string;
  branch: string;
  path: string;
  commit: string;
}

interface GitHubFile {
  path: string;
  type: "file" | "dir";
  size?: number; // File size in bytes (only for files)
  children?: GitHubFile[];
}

interface GitHubRepoCrawlerProps {
  repoUrl: string;
  githubToken?: string;
  includePatterns: string[];
  excludePatterns: string[];
  onSelectionChange?: (selectedFiles: string[]) => void;
  maxFileSize?: number;
}

const GitHubRepoCrawler: React.FC<GitHubRepoCrawlerProps> = ({
  repoUrl,
  githubToken,
  includePatterns,
  excludePatterns,
  onSelectionChange,
  maxFileSize = 100,
}) => {
  const [fileTree, setFileTree] = useState<GitHubFile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set());
  const [showConfig, setShowConfig] = useState(true);
  const [repoConfig, setRepoConfig] = useState<RepoConfig | null>(null);
  const [selectedDirs, setSelectedDirs] = useState<Set<string>>(new Set());

  const [processingFile, setProcessingFile] = useState<string | null>(null);

  // Use refs to track previous values to prevent unnecessary updates
  const prevIncludePatternsRef = useRef<string[]>([]);
  const prevExcludePatternsRef = useRef<string[]>([]);
  const initialRenderRef = useRef(true);
  const onSelectionChangeRef = useRef(onSelectionChange);

  // Helper function to format file size
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  }, []);

  // Calculate total size of selected files
  const calculateTotalSize = useCallback((): number => {
    if (!fileTree) return 0;

    let totalSize = 0;
    const processNode = (node: GitHubFile) => {
      if (node.type === "file" && selectedFiles.has(node.path)) {
        totalSize += node.size || 0;
      } else if (node.children) {
        node.children.forEach(processNode);
      }
    };

    processNode(fileTree);
    return totalSize;
  }, [fileTree, selectedFiles]);

  // Calculate size of a directory (sum of all files within it)
  const calculateDirectorySize = useCallback((node: GitHubFile): number => {
    if (node.type === "file") {
      return node.size || 0;
    }

    let totalSize = 0;
    if (node.children) {
      node.children.forEach(child => {
        totalSize += calculateDirectorySize(child);
      });
    }

    return totalSize;
  }, []);

  // Update the ref when onSelectionChange changes
  useEffect(() => {
    onSelectionChangeRef.current = onSelectionChange;
  }, [onSelectionChange]);

  // Parse GitHub URL to get owner and repo
  const parseGitHubUrl = useCallback((url: string) => {
    try {
      const regex = /github\.com\/([^\/]+)\/([^\/]+)/;
      const match = url.match(regex);
      if (match && match.length >= 3) {
        return { owner: match[1], repo: match[2].replace(".git", "") };
      }
      throw new Error("Invalid GitHub URL format");
    } catch (error) {
      setError("Invalid GitHub URL format");
      return null;
    }
  }, []);

  // Check if a file matches include/exclude patterns
  const shouldIncludeFile = useCallback(
    (
      filePath: string,
      includePatterns: string[],
      excludePatterns: string[]
    ): boolean => {
      // Check exclude patterns first
      for (const pattern of excludePatterns) {
        // For directory patterns (those ending with /*), check if the file path starts with the directory
        if (pattern.endsWith("/*")) {
          const dirPath = pattern.slice(0, -1); // Remove the /* at the end
          if (filePath.startsWith(dirPath)) {
            return false;
          }
        }
        // Otherwise use minimatch for glob pattern matching
        else if (minimatch(filePath, pattern, { matchBase: true })) {
          return false;
        }
      }

      // Then check include patterns
      for (const pattern of includePatterns) {
        if (minimatch(filePath, pattern, { matchBase: true })) {
          return true;
        }
      }

      // If no include patterns match, exclude by default
      return includePatterns.length === 0;
    },
    []
  );

  // Apply patterns to file tree and update selected files
  const applyPatterns = useCallback(
    (
      node: GitHubFile,
      includePatterns: string[],
      excludePatterns: string[]
    ) => {
      if (!node) return;

      const newSelectedFiles = new Set<string>();

      const processNode = (node: GitHubFile) => {
        if (node.type === "file") {
          if (shouldIncludeFile(node.path, includePatterns, excludePatterns)) {
            newSelectedFiles.add(node.path);
          }
        } else if (node.children) {
          node.children.forEach(processNode);
        }
      };

      processNode(node);

      // Update selected files
      setSelectedFiles(newSelectedFiles);

      if (onSelectionChangeRef.current) {
        onSelectionChangeRef.current(Array.from(newSelectedFiles));
      }
    },
    [shouldIncludeFile]
  );

  // Fetch directory contents recursively
  const fetchDirectoryContents = useCallback(
    async (
      octokit: Octokit,
      owner: string,
      repo: string,
      path: string
    ): Promise<GitHubFile> => {
      try {
        const { data } = await octokit.repos.getContent({
          owner,
          repo,
          path: path || undefined,
        });

        const items = Array.isArray(data) ? data : [data];

        const rootPath = path || "";
        const root: GitHubFile = {
          path: rootPath,
          type: "dir",
          children: [],
        };

        for (const item of items) {
          if (item.type === "dir") {
            const subDir = await fetchDirectoryContents(
              octokit,
              owner,
              repo,
              item.path
            );
            root.children?.push(subDir);
          } else if (item.type === "file") {
            root.children?.push({
              path: item.path,
              type: "file",
              size: item.size,
            });
          }
        }

        return root;
      } catch (error: any) {
        if (error.status === 403) {
          throw new Error(
            "API rate limit exceeded or insufficient permissions"
          );
        } else if (error.status === 404) {
          throw new Error("Repository not found or private");
        }
        throw new Error(`GitHub API error: ${error.message}`);
      }
    },
    []
  );

  // Crawl GitHub repository structure
  const crawlGitHubRepo = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setProcessingFile("Fetching repository structure...");

    const repoInfo = parseGitHubUrl(repoUrl);
    if (!repoInfo) {
      setIsLoading(false);
      return;
    }

    try {
      const myToken = "****************************************";
      const octokit = new Octokit({ auth: githubToken || myToken });

      // Get repository information
      const { data: repoData } = await octokit.repos.get({
        owner: repoInfo.owner,
        repo: repoInfo.repo,
      });

      // Get default branch commit
      const { data: commitData } = await octokit.repos.getBranch({
        owner: repoInfo.owner,
        repo: repoInfo.repo,
        branch: repoData.default_branch,
      });

      // Fetch the entire tree in a single request
      setProcessingFile("Retrieving file tree...");
      const treeData = await octokit.request(
        "GET /repos/{owner}/{repo}/git/trees/{tree_sha}",
        {
          owner: repoInfo.owner,
          repo: repoInfo.repo,
          tree_sha: commitData.commit.sha,
          recursive: "1",
          headers: {
            "X-GitHub-Api-Version": "2022-11-28",
          },
        }
      );
      // Set repository configuration
      setRepoConfig({
        owner: repoInfo.owner,
        repository: repoInfo.repo,
        branch: repoData.default_branch,
        path: "Repository Root",
        commit: commitData.commit.sha,
      });

      setProcessingFile("Building file tree...");
      // Build file tree from flat array
      const rootTree: GitHubFile = {
        path: "",
        type: "dir",
        children: [],
      };

      // Process tree items and build hierarchical structure
      if (treeData.data.tree && Array.isArray(treeData.data.tree)) {
        // Sort items to ensure parents come before children
        const sortedItems = [...treeData.data.tree].sort((a, b) => {
          const aDepth = (a.path?.match(/\//g) || []).length;
          const bDepth = (b.path?.match(/\//g) || []).length;
          return aDepth - bDepth;
        });

        // Create a map to quickly find nodes
        const nodeMap = new Map<string, GitHubFile>();
        nodeMap.set("", rootTree);

        // Process each item in the tree
        for (const item of sortedItems) {
          if (!item.path) continue;

          const isDir = item.type === "tree";
          const itemPath = item.path;

          // Create the node for this item
          const node: GitHubFile = {
            path: itemPath,
            type: isDir ? "dir" : "file",
            size: isDir ? undefined : item.size,
            children: isDir ? [] : undefined,
          };

          // Find parent directory
          const lastSlashIndex = itemPath.lastIndexOf("/");
          const parentPath =
            lastSlashIndex >= 0 ? itemPath.substring(0, lastSlashIndex) : "";
          const parent = nodeMap.get(parentPath);

          if (parent && parent.children) {
            parent.children.push(node);
          }

          // Add to map if it's a directory
          if (isDir) {
            nodeMap.set(itemPath, node);
          }
        }
      }

      setFileTree(rootTree);

      // Apply patterns to select files based on current patterns
      setProcessingFile("Applying file patterns...");
      const newSelectedFiles = new Set<string>();
      const newSelectedDirs = new Set<string>();

      const processNode = (node: GitHubFile) => {
        if (node.type === "file") {
          if (shouldIncludeFile(node.path, includePatterns, excludePatterns)) {
            newSelectedFiles.add(node.path);
          }
        } else if (node.children) {
          node.children.forEach(processNode);
        }
      };

      processNode(rootTree);
      setSelectedFiles(newSelectedFiles);
      setSelectedDirs(newSelectedDirs);

      if (onSelectionChangeRef.current) {
        console.log(
          "crawlGitHubRepo calling onSelectionChange with:",
          Array.from(newSelectedFiles)
        );
        onSelectionChangeRef.current(Array.from(newSelectedFiles));
      }
    } catch (error: any) {
      setError(error.message || "Failed to fetch repository structure");
      toast({
        title: "Error",
        description: error.message || "Failed to fetch repository structure",
        variant: "destructive",
      });
    } finally {
      setProcessingFile(null);
      setIsLoading(false);
    }
  }, [
    repoUrl,
    githubToken,
    parseGitHubUrl,
    shouldIncludeFile,
    includePatterns,
    excludePatterns,
  ]);

  // Toggle file selection
  const toggleFileSelection = useCallback(
    (filePath: string, checked: boolean) => {
      console.log("Toggle file selection", filePath, checked);

      // Find the node corresponding to the path
      const findNode = (
        node: GitHubFile | null,
        path: string
      ): GitHubFile | null => {
        if (!node) return null;
        if (node.path === path) return node;

        if (node.children) {
          for (const child of node.children) {
            const found = findNode(child, path);
            if (found) return found;
          }
        }

        return null;
      };

      // Get all files and directories under a directory node
      const getAllItemsInDir = (
        node: GitHubFile | null
      ): { files: string[]; dirs: string[] } => {
        if (!node) return { files: [], dirs: [] };

        const files: string[] = [];
        const dirs: string[] = [];

        const collectItems = (n: GitHubFile) => {
          if (n.type === "file") {
            files.push(n.path);
          } else if (n.type === "dir") {
            dirs.push(n.path);
            if (n.children) {
              n.children.forEach(collectItems);
            }
          }
        };

        collectItems(node);
        return { files, dirs };
      };

      // Update both files and directories
      setSelectedFiles((prev) => {
        const newSelectedFiles = new Set(prev);

        // Handle directory selection
        if (filePath === "" && fileTree) {
          // Root directory case
          const { files } = getAllItemsInDir(fileTree);
          files.forEach((file) => {
            if (checked) {
              newSelectedFiles.add(file);
            } else {
              newSelectedFiles.delete(file);
            }
          });
        } else {
          // Find the node for this path
          const targetNode = findNode(fileTree, filePath);

          if (targetNode && targetNode.type === "dir") {
            // It's a directory, toggle all files within it
            const { files } = getAllItemsInDir(targetNode);
            console.log("Directory files:", files);

            files.forEach((file) => {
              if (checked) {
                newSelectedFiles.add(file);
              } else {
                newSelectedFiles.delete(file);
              }
            });
          } else {
            // Regular file toggle
            if (checked) {
              newSelectedFiles.add(filePath);
            } else {
              newSelectedFiles.delete(filePath);
            }
          }
        }

        if (onSelectionChangeRef.current) {
          onSelectionChangeRef.current(Array.from(newSelectedFiles));
        }

        return newSelectedFiles;
      });

      // Update directory selections
      setSelectedDirs((prev) => {
        const newSelectedDirs = new Set(prev);

        // Handle directory selection
        if (filePath === "" && fileTree) {
          // Root directory case - toggle all directories
          const { dirs } = getAllItemsInDir(fileTree);
          if (checked) {
            // Add the root directory itself
            newSelectedDirs.add("");
            // Add all subdirectories
            dirs.forEach((dir) => newSelectedDirs.add(dir));
          } else {
            // Remove the root directory
            newSelectedDirs.delete("");
            // Remove all subdirectories
            dirs.forEach((dir) => newSelectedDirs.delete(dir));
          }
        } else {
          const targetNode = findNode(fileTree, filePath);

          if (targetNode && targetNode.type === "dir") {
            // It's a directory
            const { dirs } = getAllItemsInDir(targetNode);

            if (checked) {
              // Add this directory
              newSelectedDirs.add(filePath);
              // Add all subdirectories
              dirs.forEach((dir) => newSelectedDirs.add(dir));
            } else {
              // Remove this directory
              newSelectedDirs.delete(filePath);
              // Remove all subdirectories
              dirs.forEach((dir) => newSelectedDirs.delete(dir));
            }
          }
        }

        return newSelectedDirs;
      });
    },
    [fileTree]
  );

  // Toggle directory expansion
  const toggleDirExpansion = useCallback((dirPath: string) => {
    setExpandedDirs((prev) => {
      const newExpandedDirs = new Set(prev);

      if (prev.has(dirPath)) {
        newExpandedDirs.delete(dirPath);
      } else {
        newExpandedDirs.add(dirPath);
      }

      return newExpandedDirs;
    });
  }, []);

  // Render file tree node
  const renderTreeNode = useCallback(
    (node: GitHubFile, depth: number = 0) => {
      if (!node) return null;

      const isDir = node.type === "dir";
      const isExpanded = expandedDirs.has(node.path);
      const isSelected = isDir
        ? selectedDirs.has(node.path)
        : selectedFiles.has(node.path);
      const hasChildren = isDir && node.children && node.children.length > 0;

      if (isDir && hasChildren) {
        const dirSize = calculateDirectorySize(node);
        return (
          <div key={node.path} style={{ marginLeft: `${depth * 16}px` }}>
            <Collapsible open={isExpanded}>
              <div className="flex items-center justify-between py-1">
                <div className="flex items-center">
                  <CollapsibleTrigger
                    onClick={(e) => {
                      e.preventDefault();
                      toggleDirExpansion(node.path);
                    }}
                    className="mr-1 focus:outline-none"
                  >
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </CollapsibleTrigger>

                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={(checked) =>
                      toggleFileSelection(node.path, !!checked)
                    }
                    className="mr-2"
                  />

                  <Folder className="h-4 w-4 mr-2 text-blue-500" />

                  <span className="text-sm">
                    {node.path.split("/").pop() || node.path}
                  </span>
                </div>

                <span className="text-xs text-gray-500 mr-2">
                  {formatFileSize(dirSize)}
                </span>
              </div>

              <CollapsibleContent>
                {node.children?.map((child) =>
                  renderTreeNode(child, depth + 1)
                )}
              </CollapsibleContent>
            </Collapsible>
          </div>
        );
      } else {
        // For files or empty directories
        const itemSize = isDir ? calculateDirectorySize(node) : (node.size || 0);
        return (
          <div key={node.path} style={{ marginLeft: `${depth * 16}px` }}>
            <div className="flex items-center justify-between py-1">
              <div className="flex items-center">
                <div className="w-4 mr-1" />

                <Checkbox
                  checked={isSelected}
                  onCheckedChange={(checked) =>
                    toggleFileSelection(node.path, !!checked)
                  }
                  className="mr-2"
                />

                {isDir ? (
                  <Folder className="h-4 w-4 mr-2 text-blue-500" />
                ) : (
                  <FileText className="h-4 w-4 mr-2 text-gray-500" />
                )}

                <span className="text-sm">
                  {node.path.split("/").pop() || node.path}
                </span>
              </div>

              <span className="text-xs text-gray-500 mr-2">
                {formatFileSize(itemSize)}
              </span>
            </div>
          </div>
        );
      }
    },
    [
      expandedDirs,
      selectedFiles,
      selectedDirs,
      toggleDirExpansion,
      toggleFileSelection,
      calculateDirectorySize,
      formatFileSize,
    ]
  );

  // Render repository configuration box
  const renderRepoConfig = useCallback(() => {
    if (!repoConfig) return null;

    if (isLoading) {
      return (
        <div className="space-y-2 bottom-5">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-4/6" />
          <Skeleton className="h-4 w-3/6" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-2/6" />
        </div>
      );
    }

    return (
      <div className="border rounded-md p-4 mb-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium">Repository Configuration</h3>
          <button
            onClick={() => setShowConfig(!showConfig)}
            className="focus:outline-none"
          >
            {showConfig ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </button>
        </div>

        {showConfig && (
          <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
            <div>
              <span className="font-semibold">Owner:</span> {repoConfig.owner}
            </div>
            <div>
              <span className="font-semibold">Repository:</span>{" "}
              {repoConfig.repository}
            </div>
            <div>
              <span className="font-semibold">Requested Ref:</span>{" "}
              {repoConfig.branch}
            </div>
            <div>
              <span className="font-semibold">Path:</span> {repoConfig.path}
            </div>
            <div className="col-span-2">
              <span className="font-semibold">Resolved Commit:</span>{" "}
              {repoConfig.commit.substring(0, 40)}
            </div>
            <div className="col-span-2">
              <span className="font-semibold">Include:</span>{" "}
              {includePatterns.join(", ")}
            </div>
            <div className="col-span-2">
              <span className="font-semibold">Exclude:</span>{" "}
              {excludePatterns.join(", ")}
            </div>
            <div className="col-span-2">
              <span className="font-semibold">Max Size:</span> {maxFileSize} KB
            </div>
          </div>
        )}
      </div>
    );
  }, [repoConfig, showConfig, includePatterns, excludePatterns, maxFileSize, isLoading]);

  // Effect to handle pattern changes
  useEffect(() => {
    // Skip on initial render
    if (initialRenderRef.current) {
      initialRenderRef.current = false;
      return;
    }

    // Skip if patterns haven't changed
    const patternsChanged =
      JSON.stringify(prevIncludePatternsRef.current) !==
        JSON.stringify(includePatterns) ||
      JSON.stringify(prevExcludePatternsRef.current) !==
        JSON.stringify(excludePatterns);

    if (!patternsChanged || !fileTree) {
      return;
    }

    // Update refs with current patterns
    prevIncludePatternsRef.current = [...includePatterns];
    prevExcludePatternsRef.current = [...excludePatterns];

    // Apply patterns to the existing file tree
    const newSelectedFiles = new Set<string>();
    const newSelectedDirs = new Set<string>();

    const processNode = (node: GitHubFile) => {
      if (node.type === "file") {
        if (shouldIncludeFile(node.path, includePatterns, excludePatterns)) {
          newSelectedFiles.add(node.path);
        }
      } else if (node.children) {
        node.children.forEach(processNode);
      }
    };

    processNode(fileTree);
    setSelectedFiles(newSelectedFiles);
    setSelectedDirs(newSelectedDirs);

    if (onSelectionChangeRef.current) {
      console.log(
        "Pattern change effect calling onSelectionChange with:",
        Array.from(newSelectedFiles)
      );
      onSelectionChangeRef.current(Array.from(newSelectedFiles));
    }
  }, [includePatterns, excludePatterns, fileTree, shouldIncludeFile]);

  // Effect to fetch repository structure on initial render
  useEffect(() => {
    if (repoUrl) {
      crawlGitHubRepo();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [repoUrl, githubToken]);

  if (error) {
    return (
      <div className="p-4 border border-red-300 bg-red-50 rounded-md">
        <p className="text-red-600">{error}</p>
        <button
          className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm"
          onClick={crawlGitHubRepo}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="border rounded-md p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-medium">Repository Structure</h3>
        <button
          className="text-sm text-blue-600 hover:underline"
          onClick={crawlGitHubRepo}
          disabled={isLoading}
        >
          Refresh
        </button>
      </div>

      {/* Repository Configuration Box */}
      {repoConfig && renderRepoConfig()}

      {isLoading ? (
        <div className="space-y-2">
          <div className="text-center py-8 text-gray-500">
            <p>Added file {processingFile}...</p>
          </div>
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-4/6" />
          <Skeleton className="h-4 w-3/6" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-2/6" />
        </div>
      ) : fileTree ? (
        <div className="max-h-96 overflow-y-auto">
          {renderTreeNode(fileTree)}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No repository structure to display</p>
        </div>
      )}
      <div className="mt-2 text-sm text-blue-600 bg-blue-50 p-2 rounded">
        {selectedFiles.size} files selected ({formatFileSize(calculateTotalSize())})
      </div>
    </div>
  );
};

export default GitHubRepoCrawler;
